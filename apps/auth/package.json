{"name": "auth", "version": "1.0.0", "type": "module", "private": true, "scripts": {"start": "node dist/index.cjs", "dev": "tsup --watch --onSuccess \"node dist/index.cjs\"", "build": "tsup", "check-types": "tsc --noEmit", "lint": "eslint src/ --max-warnings 0", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@repo/typescript-config": "workspace:^", "@types/express": "^5.0.3", "@types/node": "^24.3.1", "ts-node": "^10.9.2", "tsup": "^8.5.0", "typescript": "^5.9.2"}, "dependencies": {"@repo/auth-utils": "workspace:^", "cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.0.0", "morgan": "^1.10.0", "nodemailer": "^6.9.15", "supertokens-node": "^21.0.2"}}