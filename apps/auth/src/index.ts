import express, { Request, Response } from "express";
import { createDefaultSuperTokensService } from "./supertoken/initialize.js";
const app = express();
const port = 3005;
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import { createAuthRouter } from "./routes/auth";

async function startServer() {
  const app = express();
  const port = 3005;

  // Initialize SuperTokens
  console.log("Initializing SuperTokens...");
  console.log(
    "SuperTokens Connection URI:",
    process.env.SUPERTOKENS_CONNECTION_URI || "http://localhost:3567"
  );
  console.log(
    "SuperTokens API Domain:",
    process.env.SUPERTOKENS_API_DOMAIN || "http://localhost:3005"
  );
  console.log(
    "SuperTokens Website Domain:",
    process.env.SUPERTOKENS_WEBSITE_DOMAIN || "http://localhost:3000"
  );

  const superTokensService = createDefaultSuperTokensService();

  try {
    console.log("Calling superTokensService.init()...");
    superTokensService.init();
    console.log("SuperTokens init() completed");

    // Test if SuperTokens is actually working
    try {
      console.log("Testing SuperTokens middleware...");
      const middleware = superTokensService.getMiddleware();
      console.log("SuperTokens middleware obtained successfully");
    } catch (middlewareError) {
      console.error("Failed to get SuperTokens middleware:", middlewareError);
      console.error("SuperTokens is not properly initialized");
      console.error(
        "Make sure SuperTokens core is running on http://localhost:3567"
      );
      process.exit(1);
    }

    console.log("SuperTokens initialized successfully");
  } catch (error) {
    console.error("Failed to initialize SuperTokens:", error);
    console.error(
      "Make sure SuperTokens core is running on http://localhost:3567"
    );
    process.exit(1);
  }

  // Middleware
  app.use(
    helmet({
      contentSecurityPolicy: false, // Disable CSP for SuperTokens
    })
  );
  app.use(
    cors({
      origin: ["http://localhost:3000", "http://localhost:3567"], // Frontend and SuperTokens
      credentials: true,
    })
  );
  app.use(morgan("combined"));
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  const authRoutes = createAuthRouter(superTokensService);
  app.use("/auth", authRoutes);

  // Health check
  app.get("/", (req, res) => {
    res.json({
      success: true,
      message: "CadetLabs Auth Service",
      timestamp: new Date().toISOString(),
    });
  });

  app.get("/health", (req, res) => {
    res.json({
      success: true,
      message: "Auth service is healthy",
      timestamp: new Date().toISOString(),
    });
  });

  // Error handling

  app.listen(port, () => {
    console.log(`Auth service running at http://localhost:${port}`);
  });
}

startServer().catch((error) => {
  console.error("Failed to start server:", error);
  process.exit(1);
});
