import { Router, Request, Response } from "express";
import nodemailer from "nodemailer";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import { verifySession } from "supertokens-node/recipe/session/framework/express";
import Session from "supertokens-node/recipe/session";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import { listUsersByAccountInfo } from "supertokens-node";
import { createResetPasswordToken } from "supertokens-node/recipe/emailpassword";
import SuperTokens from "supertokens-node";
import fs from "fs";
import path from "path";

const transporter = nodemailer.createTransport({
  host: "smtp.freesmtpservers.com",
  port: 25, // check if SSL/TLS required → sometimes 465
  secure: false, // true if using port 465
  //   auth: {
  //     user: "your-username", // if required, else remove
  //     pass: "your-password", // if required, else remove
  //   },
});

export const createAuthRouter = () => {
  const router = Router();

  // Health check route
  router.get("/health", (req: Request, res: Response) => {
    res.json({
      success: true,
      message: "Auth service is healthy",
      timestamp: new Date().toISOString(),
    });
  });
  router.post("/forgot-password", async (req, res) => {
    const { email } = req.body;

    try {
      // Find user by email using listUsersByAccountInfo
      const users = await listUsersByAccountInfo("public", { email });
      if (!users || users.length === 0) {
        return res.json({ status: "EMAIL_NOT_FOUND_ERROR" });
      }
      const user = users[0];
      const userId = user.id;

      // Create reset password token
      const tokenResponse = await EmailPassword.createResetPasswordToken(
        "public",
        userId,
        email
      );
      if (tokenResponse.status !== "OK") {
        return res.status(500).json({ status: tokenResponse.status });
      }
      const resetToken = tokenResponse.token;

      // Build reset link
      const resetLink = `http://localhost:3001/reset-password?token=${resetToken}&rid=emailpassword`;

      // Read and fill template
      const templatePath = path.resolve(
        process.cwd(),
        "src/supertoken/password-reset.html"
      );
      let html = fs.readFileSync(templatePath, "utf8");
      html = html.replace(/{{email}}/g, email);
      html = html.replace(/{{resetLink}}/g, resetLink);

      await transporter.sendMail({
        from: '"MyApp Support" <<EMAIL>>',
        to: email,
        subject: "Reset your password",
        html,
      });

      return res.json({ status: "OK" });
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  });

  // Step 2: Reset password using token
  router.post("/reset-password", async (req, res) => {
    const { token, newPassword } = req.body;

    try {
      let response = await EmailPassword.resetPasswordUsingToken(
        token,
        newPassword
      );

      if (response.status === "OK") {
        return res.json({ status: "OK" });
      } else {
        return res.json({ status: "RESET_PASSWORD_INVALID_TOKEN_ERROR" });
      }
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  });
  router.post("/verify-reset-token", async (req, res) => {
    const { token } = req.body;

    try {
      const response = await EmailPassword.consumePasswordResetToken(token);

      if (response.status === "OK") {
        return res.json({ status: "OK", userId: response.userId });
      } else {
        return res.json({ status: "INVALID_TOKEN" });
      }
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  });
  router.post("/create-user", async (req, res) => {
    const { email, password } = req.body;

    try {
      const response = await EmailPassword.signUp("public", email, password);

      if (response.status === "OK") {
        return res.json({ status: "OK", user: response.user });
      } else if (response.status === "EMAIL_ALREADY_EXISTS_ERROR") {
        return res.status(400).json({ status: "EMAIL_ALREADY_EXISTS_ERROR" });
      }
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  });
  router.get("/me", verifySession(), async (req, res) => {
    try {
      const session = req.session!; // verified by middleware
      const userId = session.getUserId();

      // Get user details (email, timeJoined, etc.)
      const userInfo = await EmailPassword.getUserById(userId);

      // Get user metadata (custom fields)
      const metadata = await UserMetadata.getUserMetadata(userId);

      return res.json({
        status: "OK",
        user: {
          id: userId,
          email: userInfo?.emails[0],
          timeJoined: userInfo?.timeJoined,
          metadata: metadata.metadata, // all custom metadata
        },
      });
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  });
  router.post("/create-user", async (req, res) => {
    const { email, password, metadata } = req.body;

    try {
      // Step 1: Create the user
      const response = await EmailPassword.signUp("public", email, password);

      if (response.status === "OK") {
        const user = response.user;

        // Step 2: Store metadata (if provided)
        if (metadata) {
          await UserMetadata.updateUserMetadata(user.id, metadata);
        }

        // Fetch full metadata again to return
        const fullMetadata = await UserMetadata.getUserMetadata(user.id);

        return res.json({
          status: "OK",
          user: {
            id: user.id,
            email: user.emails[0],
            timeJoined: user.timeJoined,
            metadata: fullMetadata.metadata,
          },
        });
      } else if (response.status === "EMAIL_ALREADY_EXISTS_ERROR") {
        return res.status(400).json({ status: "EMAIL_ALREADY_EXISTS_ERROR" });
      }
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  });
  router.post("/generate-token", async (req, res) => {
    const { userId } = req.body;

    try {
      // Create session without needing request/response objects
      const session = await Session.createNewSessionWithoutRequestResponse(
        "public",
        userId,
        {},
        {}
      );
      const tokens = session.getAllSessionTokensDangerously();

      return res.json({
        status: "OK",
        userId,
        tokens,
      });
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Failed to generate token" });
    }
  });
  return router;
};
