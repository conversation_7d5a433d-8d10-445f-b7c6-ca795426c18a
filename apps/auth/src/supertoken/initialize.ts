import supertokens from "supertokens-node";
import EmailVerification from "supertokens-node/recipe/emailverification";
import Session from "supertokens-node/recipe/session";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import { middleware, errorHandler } from "supertokens-node/framework/express";
import { SMTPService } from "supertokens-node/recipe/emailpassword/emaildelivery";

export interface SuperTokensConfig {
  connectionURI: string;
  appInfo: {
    appName: string;
    apiDomain: string;
    websiteDomain: string;
    apiBasePath?: string;
    websiteBasePath?: string;
  };
}
export class SuperTokensService {
  private config: SuperTokensConfig;
  private initialized = false;

  constructor(config: SuperTokensConfig) {
    this.config = config;
  }

  /**
   * Initialize SuperTokens with the provided configuration
   */
  init(): void {
    supertokens.init({
      framework: "express",
      supertokens: {
        connectionURI: "http://localhost:3567",
      },
      appInfo: {
        appName: "CadetLabs",
        apiDomain: "http://localhost:3005", // Auth app domain
        websiteDomain: "http://localhost:3000", // Frontend domain
        apiBasePath: "/auth",
        websiteBasePath: "/auth",
      },
      recipeList: [
        EmailPassword.init({
          emailDelivery: {
            service: new SMTPService({
              smtpSettings: {
                host: "smtp.freesmtpservers.com",
                port: 25,
                secure: false, // Use TLS
                from: {
                  name: "SuperTokens Demo App",
                  email: " <EMAIL>", // Replace with your domain
                },
                // Note: smtp.freesmtpservers.com typically doesn't require authentication
                // but you may need to configure these based on the specific server
                authUsername: undefined,
                password: "",
              },
              override: (originalImplementation) => {
                return {
                  ...originalImplementation,
                  getContent: async (input) => {
                    if (input.type === "PASSWORD_RESET") {
                      const emailData =
                        EmailTemplateRenderer.getPasswordResetEmail({
                          email: input.user.email,
                          resetLink: input.passwordResetLink,
                        });
                      return {
                        body: emailData.html,
                        isHtml: true,
                        subject: emailData.subject,
                        toEmail: input.user.email,
                      };
                    }
                    // Fall back to original implementation for other email types
                    return originalImplementation.getContent(input);
                  },
                };
              },
            }),
          },
        }),
        Session.init({
          cookieSecure: process.env.NODE_ENV === "production",
          cookieSameSite: "lax",
          exposeAccessTokenToFrontendInCookieBasedAuth: true,
        }),
      ],
    });

    this.initialized = true;
  }
  getMiddleware() {
    if (!this.initialized) {
      throw new Error("SuperTokens not initialized. Call init() first.");
    }
    return middleware();
  }
}
export function createDefaultSuperTokensService(): SuperTokensService {
  return new SuperTokensService({
    connectionURI: "http://localhost:3567",
    appInfo: {
      appName: "CadetLabs",
      apiDomain: "http://localhost:3005", // Auth app domain
      websiteDomain: "http://localhost:3000", // Frontend domain
      apiBasePath: "/auth",
      websiteBasePath: "/auth",
    },
  });
}
