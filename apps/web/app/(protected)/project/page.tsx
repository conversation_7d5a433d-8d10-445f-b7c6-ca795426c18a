"use client";
import React from "react";
import { Table } from "@/components/table";
import { ColumnDef } from "@tanstack/react-table";

type Vessel = {
  id: string;
  vesselName: string;
  code: string;
  company: string;
  vesselType: string;
  totalFiles: number;
  status: string;
};

const data: Vessel[] = [
  {
    id: "1",
    vesselName: "Atlas",
    code: "AT001",
    company: "XY Shipping",
    vesselType: "Bulk Carrier",
    totalFiles: 310,
    status: "Processing",
  },
  {
    id: "2",
    vesselName: "Coral Sea",
    code: "ST002",
    company: "XY Shipping",
    vesselType: "Container",
    totalFiles: 240,
    status: "Processing",
  },
  {
    id: "3",
    vesselName: "Pacific Fortune",
    code: "PF003",
    company: "XY Shipping",
    vesselType: "Tanker",
    totalFiles: 440,
    status: "Processing",
  },
  {
    id: "4",
    vesselName: "Endurance",
    code: "ET004",
    company: "XY Shipping",
    vesselType: "Bulk Carrier",
    totalFiles: 340,
    status: "Processing",
  },
  {
    id: "5",
    vesselName: "Star Vista",
    code: "ST005",
    company: "XY Shipping",
    vesselType: "Bulk Carrier",
    totalFiles: 340,
    status: "Processing",
  },
];

const columns: ColumnDef<Vessel>[] = [
  {
    header: "Vessel Name",
    accessorKey: "vesselName",
    cell: ({ row }) => (
      <div className="flex items-center gap-3">
        <div className="h-12 w-12 rounded bg-gray-100" /> {/* placeholder icon */}
        <div>
          <div className="font-semibold">{row.original.vesselName}</div>
          <div className="text-xs text-gray-500">{row.original.code}</div>
          <div className="text-xs text-gray-500">{row.original.company}</div>
        </div>
      </div>
    ),
  },
  {
    header: "Vessel Type",
    accessorKey: "vesselType",
  },
  {
    header: "Total Files",
    accessorKey: "totalFiles",
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ getValue }) => <span className="text-gray-700">{getValue() as string}</span>,
  },
  {
    header: "Actions",
    id: "actions",
    cell: () => (
      <div className="flex items-center gap-2">
        <button className="rounded border px-3 py-1 text-sm">Edit</button>
        <button className="rounded p-2 hover:bg-gray-100">
          <span className="text-lg">⋮</span>
        </button>
      </div>
    ),
  },
];

export default function VesselTable() {
  // return <Table data={data} columns={columns} rowKey="id" />;
  return <div></div>;
}
