"use client";

import * as React from "react";
import logo from "../../../public/logo.png";
import Image from "next/image";

import HomePage from "@/components/pages/HomePage";
import { redirect } from "next/navigation";
import { ForgotPasswordform } from "@/components/forms/ForgotPassword";

export default function forgotPasswordPage() {
  const handleLogin = (data: { email: string }) => {
    console.log("forgotpassword mail attempt:", data);
    // TODO: integrate API call
  };

  const handleAlreadyHaveAccount = () => {
    console.log("Already HaveAccount clicked");
    redirect("/login");
  };

  return (
    <HomePage
      FormComponent={
        <ForgotPasswordform
          onSubmit={handleLogin}
          onHaveAccount={handleAlreadyHaveAccount}
          title="Forgot Password"
          imageComponent={
            <Image src={logo} alt="Cadet Labs" width={40} height={40} className="mr-2" />
          }
        />
      }
    />
  );
}

// "use client";

// import * as React from "react";
// import logo from "../../../public/logo.png";
// import Image from "next/image";

// import HomePage from "../../../components/pages/HomePage";
// import { redirect } from "next/navigation";
// import { ForgotPasswordform } from "../../../components/forms/ForgotPassword";

// // shadcn/ui Dialog for modal
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogDescription,
//   DialogFooter,
// } from "@/components/ui/dialog";
// import { Button } from "@/components/ui/button";

// export default function ForgotPasswordPage() {
//   const [open, setOpen] = React.useState(false);
//   const [message, setMessage] = React.useState("");
//   const [isError, setIsError] = React.useState(false);

//   const handleLogin = (data: { email: string }) => {
//     console.log("forgotpassword mail attempt:", data.email);

//     // Dummy validation — replace with API later
//     if (data.email && data.email.includes("@")) {
//       setMessage("A password recovery mail has been sent to your email.");
//       setIsError(false);
//       setOpen(true);
//     } else {
//       setMessage("Invalid email address. Please try again.");
//       setIsError(true);
//       setOpen(true);
//     }
//   };

//   const handleAlreadyHaveAccount = () => {
//     console.log("Already Have Account clicked");
//     redirect("/login");
//   };

//   return (
//     <>
//       <HomePage
//         FormComponent={
//           <ForgotPasswordform
//             onSubmit={handleLogin}
//             onHaveAccount={handleAlreadyHaveAccount}
//             title="Forgot Password"
//             imageComponent={
//               <Image
//                 src={logo}
//                 alt="Cadet Labs"
//                 width={40}
//                 height={40}
//                 className="mr-2"
//               />
//             }
//           />
//         }
//       />

//       {/* Modal */}
//       <Dialog open={open} onOpenChange={setOpen}>
//         <DialogContent>
//           <DialogHeader>
//             <DialogTitle>{isError ? "Error" : "Success"}</DialogTitle>
//             <DialogDescription>{message}</DialogDescription>
//           </DialogHeader>
//           <DialogFooter>
//             <Button onClick={() => setOpen(false)}>OK</Button>
//           </DialogFooter>
//         </DialogContent>
//       </Dialog>
//     </>
//   );
// }
