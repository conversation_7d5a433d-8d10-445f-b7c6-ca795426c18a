"use client";

import * as React from "react";
import logo from "@/public/logo.png";
import Image from "next/image";
import HomePage from "@/components/pages/HomePage";
import { LoginForm } from "@/components/forms/LoginForm";
import { redirect } from "next/navigation";

export default function LoginPage() {
  const handleLogin = (data: { email: string; password: string }) => {
    console.log("Login attempt:", data);
    // TODO: integrate API call
  };

  const handleForgotPassword = () => {
    console.log("Forgot password clicked");
    redirect("/forgot-password");
  };

  return (
    <HomePage
      FormComponent={
        <LoginForm
          onSubmit={handleLogin}
          onForgotPassword={handleForgotPassword}
          title="Dashboard Log In"
          imageComponent={
            <Image src={logo} alt="Cadet Labs" width={40} height={40} className="mr-2" />
          }
        />
      }
    />
  );
}
