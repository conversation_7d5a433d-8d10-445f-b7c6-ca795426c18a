"use client";

import * as React from "react";
import logo from "@/public/logo.png";
import Image from "next/image";
import HomePage from "@/components/pages/HomePage";
import { redirect } from "next/navigation";
import { NewPasswordForm } from "@/components/forms/NewPasswordForm";

export default function LoginPage() {
  const handleLogin = (data: { password: string; confirmPassword: string }) => {
    console.log("Login attempt:", data);
    // TODO: integrate API call
  };

  const handleForgotPassword = () => {
    console.log("Forgot password clicked");
    redirect("/forgot-password");
  };

  return (
    <HomePage
      FormComponent={
        <NewPasswordForm
          onSubmit={handleLogin}
          imageComponent={
            <Image src={logo} alt="Cadet Labs" width={40} height={40} className="mr-2" />
          }
        />
      }
    />
  );
}
