import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import { Input } from "@repo/ui/components/input";
import * as React from "react";

export interface ForgotPasswordProps {
  onSubmit?: (data: { email: string }) => void;
  isLoading?: boolean;
  onHaveAccount?: () => void;
  imageComponent?: React.ReactNode; // Logo passed in
  title?: string;
}

const ForgotPasswordform = React.forwardRef<HTMLDivElement, ForgotPasswordProps>(
  ({ onSubmit, isLoading = false, onHaveAccount, imageComponent, title }, ref) => {
    const [email, setEmail] = React.useState("");

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit?.({ email });
    };

    return (
      <div ref={ref} className="w-full max-w-md">
        <Card className="w-full border-0 shadow-none">
          {/* Logo + Title */}
          <CardHeader className="text-left">
            <div className="mb-4 flex flex-col items-start">{imageComponent}</div>
            <div>
              <h1 className="mb-0 text-3xl font-bold leading-snug text-teal-600">
                PMS Asset Builder
              </h1>
              <h1 className="text-2xl leading-snug text-teal-600">{title}</h1>
              <p className="text-xs text-gray-500">No worries, we’ll send you reset instructions</p>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Email */}
              <div className="space-y-1">
                <div className="relative flex w-full max-w-sm items-center gap-2">
                  <Input
                    id="email"
                    type="email"
                    placeholder="Email Address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10 pr-10 focus:border-teal-500 focus:outline-none focus:ring-teal-500"
                    required
                    disabled={isLoading}
                  />
                  <Icons.mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                </div>
              </div>

              <div>
                <Button
                  type="submit"
                  className="w-full rounded-lg bg-teal-600 py-2 text-white hover:bg-teal-600"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      Validating mail address...
                    </>
                  ) : (
                    <>
                      Submit
                      <Icons.arrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>

                <div className="text-left">
                  <button
                    type="button"
                    onClick={onHaveAccount}
                    className="cursor-pointer text-xs text-gray-500 underline hover:text-gray-700"
                    disabled={isLoading}
                  >
                    Already have an account?
                  </button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }
);

ForgotPasswordform.displayName = "ForgotPasswordForm";
export { ForgotPasswordform };
