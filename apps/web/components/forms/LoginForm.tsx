import * as React from "react";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import { Input } from "@repo/ui/components/input";

export interface LoginFormProps {
  onSubmit?: (data: { email: string; password: string }) => void;
  isLoading?: boolean;
  onForgotPassword?: () => void;
  imageComponent?: React.ReactNode; // Logo passed in
  title?: string;
}

const LoginForm = React.forwardRef<HTMLDivElement, LoginFormProps>(
  ({ onSubmit, isLoading = false, onForgotPassword, imageComponent, title }, ref) => {
    const [email, setEmail] = React.useState("");
    const [password, setPassword] = React.useState("");
    const [showPassword, setShowPassword] = React.useState(false);

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit?.({ email, password });
    };

    return (
      <div ref={ref} className="w-full max-w-md">
        <Card className="w-full border-0 shadow-none">
          {/* Logo + Title */}
          <CardHeader className="text-left">
            <div className="mb-4 flex flex-col items-start">{imageComponent}</div>
            <div>
              <h1 className="text-primary mb-0 text-3xl font-bold leading-snug">
                PMS Asset Builder
              </h1>
              <h1 className="text-primary text-2xl leading-snug">{title}</h1>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Email */}
              <div className="space-y-1">
                <div className="relative flex w-full max-w-sm items-center gap-2">
                  <Input
                    id="email"
                    type="email"
                    placeholder="Email Address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="px-10"
                    required
                    disabled={isLoading}
                  />
                  <Icons.mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                </div>
              </div>

              {/* Password */}
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showPassword ? "text" : "password"}
                  placeholder="Confirm Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="px-10"
                  required
                  disabled={isLoading}
                />
                <Icons.lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 hover:text-gray-600"
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <Icons.eyeOff className="h-4 w-4" />
                  ) : (
                    <Icons.eye className="h-4 w-4" />
                  )}
                </button>
              </div>

              <div>
                {/* Login button */}
                <Button type="submit" className="py- w-full rounded-lg" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      Logging in...
                    </>
                  ) : (
                    <>
                      Login
                      <Icons.arrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>

                {/* Forgot password */}
                <div className="text-left">
                  <button
                    type="button"
                    onClick={onForgotPassword}
                    className="cursor-pointer text-xs text-gray-500 underline hover:text-gray-700"
                    disabled={isLoading}
                  >
                    Forgot Password?
                  </button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }
);

LoginForm.displayName = "LoginForm";
export { LoginForm };
