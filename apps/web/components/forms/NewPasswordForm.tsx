"use client";

import * as React from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import { Input } from "@repo/ui/components/input";

export interface NewPasswordFormProps {
  onSubmit?: (data: { password: string; confirmPassword: string }) => void;
  isLoading?: boolean;
  email?: string; // email to show in subtitle
  imageComponent?: React.ReactNode;
}

const NewPasswordForm = React.forwardRef<HTMLDivElement, NewPasswordFormProps>(
  ({ onSubmit, isLoading = false, email, imageComponent }, ref) => {
    const [password, setPassword] = React.useState("");
    const [confirmPassword, setConfirmPassword] = React.useState("");
    const [showPassword, setShowPassword] = React.useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit?.({ password, confirmPassword });
    };

    return (
      <div ref={ref} className="w-full max-w-md">
        <Card className="w-full border-0 shadow-none">
          {/* Logo + Title */}
          <CardHeader className="text-left">
            <div className="mb-4 flex flex-col items-start">{imageComponent}</div>
            <div>
              <h1 className="text-3xl font-bold leading-snug text-teal-600">PMS Asset Builder</h1>
              <h2 className="mb-2 text-2xl leading-snug text-teal-600">Set New Password</h2>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Password */}
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 pr-10 focus:border-teal-500 focus:outline-none focus:ring-teal-500"
                  required
                  disabled={isLoading}
                />
                <Icons.lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <Icons.eyeOff className="h-4 w-4" />
                  ) : (
                    <Icons.eye className="h-4 w-4" />
                  )}
                </button>
              </div>

              {/* Confirm Password */}
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm Password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="pl-10 pr-10 focus:border-teal-500 focus:outline-none focus:ring-teal-500"
                  required
                  disabled={isLoading}
                />
                <Icons.lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  disabled={isLoading}
                >
                  {showConfirmPassword ? (
                    <Icons.eyeOff className="h-4 w-4" />
                  ) : (
                    <Icons.eye className="h-4 w-4" />
                  )}
                </button>
              </div>

              {/* Submit button */}
              <Button
                type="submit"
                className="w-full rounded-lg bg-teal-600 py-3 text-lg text-white hover:bg-teal-700"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    Setting Password...
                  </>
                ) : (
                  <>
                    Set New Password
                    <Icons.arrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }
);

NewPasswordForm.displayName = "NewPasswordForm";
export { NewPasswordForm };
