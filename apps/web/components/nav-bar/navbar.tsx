"use client";
import React, { useState } from "react";
import logo from "../../public/logo.png";
import personPic from "../../public/person.png";
import Image from "next/image";
import { Popover, PopoverTrigger, PopoverContent } from "@repo/ui/components/popover";
import { Calendar } from "@repo/ui/components/calendar";
import { Icons } from "@repo/ui/components/icons";
import dayjs from "dayjs";
import updateLocale from "dayjs/plugin/updateLocale";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { useRouter } from "next/navigation";
import "dayjs/locale/en";

dayjs.extend(updateLocale);
dayjs.locale("en");

const menuConfig = [
  {
    title: "Profile",
    description: "View and edit your personal details and profile settings.",
    icon: Icons.user,
    path: "profile",
  },
  {
    title: "Settings",
    description: "Manage your account settings, theme, and notifications.",
    icon: Icons.settings,
    path: "settings",
  },
  {
    title: "My Activity",
    description: "See recent activity or open projects you've worked on.",
    icon: Icons.history,
    path: "activity",
  },
  {
    title: "User Management",
    description: "Manage users, roles, and permissions.",
    icon: Icons.users,
    path: "users",
  },
  {
    title: "Tenant Management",
    description: "Manage tenants, roles, and permissions.",
    icon: Icons.boxes,
    path: "tenant",
  },
  {
    title: "Help & Support",
    description: "Get help, view documentation, or contact support.",
    icon: Icons.headset,
    path: "help",
  },
  {
    title: "Logout",
    description: "Securely sign out of your account.",
    icon: Icons.logout,
    path: "login",
  },
];

const Navbar: React.FC = () => {
  const [date, setDate] = useState(new Date());

  const { push } = useRouter();

  return (
    <nav className="h-[90px] w-full border bg-white">
      <div className="flex h-full items-center justify-between px-4">
        {/* Left Logo */}
        <div className="flex items-center space-x-2">
          <Image src={logo} alt="Cadetlabs" className="h-[49px] w-[56px] object-contain" />
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-6">
          {/* Calendar */}
          <div className="flex items-center space-x-2">
            <Icons.calendar className="size-10 text-teal-500" />
            <Popover>
              <PopoverTrigger asChild>
                <div className="cursor-pointer">
                  <p className="text-xs text-gray-600">Today</p>
                  <p className="text-sm font-medium text-gray-800">
                    {dayjs(new Date()).format("DD MMM YYYY").toUpperCase()}
                  </p>
                </div>
              </PopoverTrigger>
              <PopoverContent className="w-auto rounded-xl bg-white p-2 shadow-lg">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  className="rounded-lg border"
                  required
                />
              </PopoverContent>
            </Popover>
            <Icons.chevronDown className="size-4 text-gray-500" />
          </div>

          {/* Divider */}
          <span className="h-6 w-px bg-gray-300"></span>

          {/* User Profile */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {/* Profile Image */}
              <div className="h-[43px] w-[44px] overflow-hidden rounded-md">
                <Image src={personPic} alt="Profile" className="h-full w-full object-cover" />
              </div>

              {/* Name + Role */}
              <div className="leading-tight">
                <h1 className="text-sm font-medium text-gray-800">Sanjay M</h1>
                <p className="text-[11px] text-gray-500">Admin</p>
              </div>

              {/* Chevron */}
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Icons.chevronDown className="size-4 text-gray-500" />
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[300px]" side="bottom" align="end" sideOffset={4}>
                  {menuConfig.map((item) => (
                    <DropdownMenuItem
                      className="cursor-pointer"
                      key={item.title}
                      onClick={() => push(item.path)}
                    >
                      <div className="flex items-start gap-3 p-2">
                        <item.icon className="size-7 text-teal-500" />
                        <div className="flex flex-col">
                          <h1 className="text-sm font-medium text-gray-900">{item.title}</h1>
                          <p className="text-xs text-gray-500">{item.description}</p>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Notification + Settings */}
            <div className="flex items-center space-x-3">
              <div className="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full border border-gray-200 transition hover:bg-gray-100">
                <Icons.bell className="size-4 text-gray-600" />
              </div>
              <div className="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full border border-gray-200 transition hover:bg-gray-100">
                <Icons.settings className="size-4 text-gray-600" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
