"use client";

import * as React from "react";
import shipImg from "../../../public/ship.png";
import logo from "../../../public/logo.png";
import Image from "next/image";

export default function HomePage({
  FormComponent,
}: {
  FormComponent: React.ReactNode;
}) {
  const handleLogin = (data: { email: string; password: string }) => {
    console.log("Login attempt:", data);
    // TODO: integrate API call
  };

  const handleForgotPassword = () => {
    console.log("Forgot password clicked");
    // TODO: show forgot password flow
  };

  return (
    <div className="min-h-screen flex">
      <div className="hidden lg:flex lg:w-1/2 relative p-12">
        <div className="relative w-full rounded-2xl overflow-hidden shadow-xl">
          {/* <Image src={shipImg} alt="Ship" fill className="object-cover" /> */}

          {/* <video
            autoPlay
            loop
            playsInline
            className="absolute inset-0 w-full h-full object-cover"
          >
            <source src="/intro.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video> */}

          <video
            autoPlay
            muted
            playsInline
            className="absolute inset-0 w-full h-full object-cover object-[60%_center]"
          >
            <source src="/intro2.mp4" type="video/mp4" />
          </video>

          <div className="absolute inset-0 bg-black/20" />

          <div className="relative z-10 flex flex-col justify-end items-center w-full h-full p-12 text-white text-center">
            <h1 className="text-xs font-bold uppercase tracking-wide mb-2">
              ONE PLATFORM FOR ALL PROJECT DOCUMENTATION
            </h1>
            <h2 className="text-3xl mb-2">
              Clarity, Control, <br /> Collaboration
            </h2>
            <p className="text-xs text-gray-200 mb-10">
              The Most Reliable Platform for Seamless Project File Management
            </p>
          </div>
        </div>
      </div>

      <div className="w-full lg:w-1/2 flex items-center justify-center bg-white">
        {FormComponent}
      </div>
    </div>
  );
}
