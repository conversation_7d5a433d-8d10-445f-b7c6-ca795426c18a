"use client";
import React from "react";
import { Button } from "@repo/ui/components/button";

interface NavButton {
  name: string;
  onClick: () => void;
}

interface SubNavBarProps {
  buttons?: NavButton[];
  actionButton?: NavButton; // optional right-side button
}

export const SubNavBar: React.FC<SubNavBarProps> = ({ buttons, actionButton }) => {
  return (
    <div className="flex w-full items-center justify-between py-2">
      {/* Left Nav Buttons */}
      <div className="flex items-center gap-3">
        <Button
          key="dashboard"
          onClick={() => console.log("dashboard")}
          className="border-[#f0f7f6] bg-white text-black"
        >
          Dashboard
        </Button>
        <Button
          key="dashboard"
          onClick={() => console.log("dashboard")}
          className="border-[#f0f7f6] bg-white text-black"
        >
          Project
        </Button>
        <Button
          key="dashboard"
          onClick={() => console.log("dashboard")}
          className="border-[#f0f7f6] bg-white text-black"
        >
          Tools
        </Button>
      </div>

      {/* Right Action Button */}
      {actionButton && <Button onClick={actionButton.onClick}>{actionButton.name}</Button>}
    </div>
  );
};
