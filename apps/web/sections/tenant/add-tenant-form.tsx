"use client";
import React from "react";
import { useForm } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";

const config = [
  {
    name: "Company Name",
    key: "companyName",
    validation: { required: "Company name is required" },
  },
  {
    name: "Tenant ID",
    key: "tenantId",
    validation: { required: "Tenant ID is required" },
  },
  {
    name: "Admin Name",
    key: "adminName",
    validation: { required: "Admin name is required" },
  },
  {
    name: "Admin Email ID",
    key: "adminEmail",
    validation: {
      required: "Admin email is required",
      pattern: {
        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: "Invalid email format",
      },
    },
  },
  {
    name: "Subscription Type",
    key: "subscriptionType",
    validation: { required: "Subscription type is required" },
  },
  {
    name: "Contact Number",
    key: "contactNumber",
    validation: {
      required: "Contact number is required",
      pattern: {
        value: /^[0-9]{10}$/,
        message: "Contact number must be 10 digits",
      },
    },
  },
  {
    name: "Address",
    key: "address",
    validation: { required: "Address is required" },
  },
  {
    name: "User Capacity",
    key: "userCapacity",
    validation: {
      required: "User capacity is required",
      pattern: {
        value: /^[0-9]+$/,
        message: "User capacity must be a number",
      },
    },
  },
];

const AddTenantForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const onSubmit = (data: Record<string, any>) => {
    console.log("Form Data:", data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-md space-y-4">
      {config.map((field) => (
        <div key={field.key} className="flex flex-col space-y-1">
          {/* <label className="text-sm font-medium">{field.name}</label> */}
          <Input
            {...register(field.key, field.validation)}
            placeholder={field.name}
            className="h-10"
          />
          {errors[field.key] && (
            <span className="text-xs text-red-500">{errors[field.key]?.message as string}</span>
          )}
        </div>
      ))}

      <Button type="submit" className="w-full">
        Register
      </Button>
    </form>
  );
};

export default AddTenantForm;
