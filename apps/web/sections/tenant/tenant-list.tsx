"use client";

import type { ColumnDef } from "@tanstack/react-table";

import { But<PERSON> } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import {
  TableProvider,
  TableHeader,
  TableHeaderGroup,
  TableHead,
  TableColumnHeader,
  TableBody,
  TableRow,
  TableCell,
} from "./tenant-table";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

type Tenant = {
  id: string;
  companyName: string;
  tenantId: string;
  adminName: string;
  subscriptionType: string;
  email: string;
  contact: string;
};

const userData: Tenant[] = [
  {
    id: "1",
    companyName: "Manuale",
    tenantId: "001",
    adminName: "Caligua",
    subscriptionType: "Premium",
    email: "<EMAIL>",
    contact: "Admin",
  },
  {
    id: "2",
    companyName: "Manuale",
    tenantId: "001",
    adminName: "Caligua",
    subscriptionType: "Premium",
    email: "<EMAIL>",
    contact: "Admin",
  },
  {
    id: "3",
    companyName: "Manuale",
    tenantId: "001",
    adminName: "Caligua",
    subscriptionType: "Premium",
    email: "<EMAIL>",
    contact: "Admin",
  },
  {
    id: "4",
    companyName: "Manuale",
    tenantId: "001",
    adminName: "Caligua",
    subscriptionType: "Premium",
    email: "<EMAIL>",
    contact: "Admin",
  },
  {
    id: "5",
    companyName: "Manuale",
    tenantId: "001",
    adminName: "Caligua",
    subscriptionType: "Premium",
    email: "<EMAIL>",
    contact: "Admin",
  },
];

const columns: ColumnDef<Tenant>[] = [
  {
    accessorKey: "companyName",
    header: ({ column }) => <TableColumnHeader column={column} title="Company Name" />,
    cell: ({ row }) => <div className="font-medium">{row.getValue("companyName")}</div>,
    enableSorting: false,
  },
  {
    accessorKey: "tenantId",
    header: ({ column }) => <TableColumnHeader column={column} title="Tenant ID" />,
    cell: ({ row }) => <div className="font-medium">{row.getValue("tenantId")}</div>,
    enableSorting: false,
  },
  {
    accessorKey: "adminName",
    header: ({ column }) => <TableColumnHeader column={column} title="Admin Name" />,
    cell: ({ row }) => <div className="text-muted-foreground">{row.getValue("adminName")}</div>,
    enableSorting: false,
  },
  {
    accessorKey: "subscriptionType",
    header: ({ column }) => <TableColumnHeader column={column} title="Subscription Type" />,
    cell: ({ row }) => (
      <div className="text-muted-foreground">{row.getValue("subscriptionType")}</div>
    ),
    enableSorting: false,
  },
  {
    accessorKey: "email",
    header: ({ column }) => <TableColumnHeader column={column} title="Email" />,
    cell: ({ row }) => <div className="font-medium">{row.getValue("email")}</div>,
    enableSorting: false,
  },
  {
    accessorKey: "contact",
    header: ({ column }) => <TableColumnHeader column={column} title="Contact" />,
    cell: ({ row }) => <div className="font-medium">{row.getValue("contact")}</div>,
    enableSorting: false,
  },
  {
    id: "actions",
    header: "Action",
    cell: ({ row }) => {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <Icons.moreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>Activate</DropdownMenuItem>
            <DropdownMenuItem className="text-destructive">Deactivate</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export default function TenantTable() {
  return (
    <div className="bg-background rounded-sm p-6">
      <div className="mx-auto">
        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" className="p-2">
              <Icons.arrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-foreground text-2xl font-semibold">Tenant Management</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
              <Icons.download className="h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
              <Icons.sortAsc className="h-4 w-4" />
              Sort
            </Button>
            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
              <Icons.filter className="h-4 w-4" />
              Filter
            </Button>
          </div>
        </div>

        {/* Table */}
        <div className="bg-card rounded-lg border">
          <TableProvider columns={columns} data={userData}>
            <TableHeader className="border-none">
              {({ headerGroup }) => (
                <TableHeaderGroup headerGroup={headerGroup}>
                  {({ header }) => (
                    <TableHead
                      header={header}
                      className="text-muted-foreground h-12 px-6 text-left align-middle font-medium"
                    />
                  )}
                </TableHeaderGroup>
              )}
            </TableHeader>
            <TableBody className="border-none">
              {({ row }) => (
                <TableRow row={row} className="rounded-md border-none bg-[#f0f7f6] p-1">
                  {({ cell }) => <TableCell cell={cell} className="px-6 py-4 align-middle" />}
                </TableRow>
              )}
            </TableBody>
          </TableProvider>
        </div>
      </div>
    </div>
  );
}
