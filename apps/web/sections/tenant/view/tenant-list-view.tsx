"use client";
import React, { useState } from "react";
import { SearchBar } from "@/components/search-bar";
import TenantTable from "../tenant-list";
import { SubNavBar } from "@/components/sub-nav-bar";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import AddTenantFormView from "./add-tenant-form-view";

const TenantListView: React.FC = () => {
  const [open, setOpen] = useState(false);
  return (
    <div className="h-[90vh] w-full bg-[#f0f7f6] p-10">
      <SearchBar />

      <Dialog open={open} onOpenChange={setOpen}>
        <div className="py-3">
          <SubNavBar actionButton={{ name: "Add Tenant", onClick: () => setOpen(true) }} />
        </div>
        <TenantTable />

        <DialogContent className="sm:max-w-[500px]">
          <AddTenantFormView />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TenantListView;
