"use client";
import React from "react";
import { useForm } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";

const config = [
  {
    name: "First Name",
    key: "firstName",
    validation: { required: "First name is required" },
  },
  {
    name: "Last Name",
    key: "lastName",
    validation: { required: "Last name is required" },
  },
  {
    name: "Display Name",
    key: "displayName",
    validation: { required: "Display name is required" },
  },
  {
    name: "Email ID",
    key: "email",
    validation: {
      required: "Email is required",
      pattern: {
        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: "Invalid email format",
      },
    },
  },
  {
    name: "User Role",
    key: "userRole",
    validation: { required: "User role is required" },
  },
];

const AddUserForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const onSubmit = (data: Record<string, any>) => {
    console.log("Form Data:", data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-md space-y-4">
      {config.map((field) => (
        <div key={field.key} className="flex flex-col space-y-1">
          {/* <label className="text-sm font-medium">{field.name}</label> */}
          <Input
            {...register(field.key, field.validation)}
            placeholder={field.name}
            className="h-10"
          />
          {errors[field.key] && (
            <span className="text-xs text-red-500">{errors[field.key]?.message as string}</span>
          )}
        </div>
      ))}

      <Button type="submit" className="w-full">
        Register
      </Button>
    </form>
  );
};

export default AddUserForm;
