"use client";

import type { ColumnDef } from "@tanstack/react-table";

import { But<PERSON> } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import {
  TableProvider,
  TableHeader,
  TableHeaderGroup,
  TableHead,
  TableColumnHeader,
  TableBody,
  TableRow,
  TableCell,
} from "./user-table";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  email: string;
  role: string;
};

const userData: User[] = [
  {
    id: "1",
    firstName: "Manuale",
    lastName: "Caligua",
    displayName: "Manuala Caligua",
    email: "<EMAIL>",
    role: "Admin",
  },
  {
    id: "2",
    firstName: "Manuale",
    lastName: "Caligua",
    displayName: "Manuala Caligua",
    email: "<EMAIL>",
    role: "Admin",
  },
  {
    id: "3",
    firstName: "Manuale",
    lastName: "Caligua",
    displayName: "Manuala Caligua",
    email: "<EMAIL>",
    role: "Admin",
  },
  {
    id: "4",
    firstName: "Manuale",
    lastName: "Caligua",
    displayName: "Manuala Caligua",
    email: "<EMAIL>",
    role: "Admin",
  },
  {
    id: "5",
    firstName: "Manuale",
    lastName: "Caligua",
    displayName: "Manuala Caligua",
    email: "<EMAIL>",
    role: "Admin",
  },
];

const columns: ColumnDef<User>[] = [
  {
    accessorKey: "firstName",
    header: ({ column }) => <TableColumnHeader column={column} title="First Name" />,
    cell: ({ row }) => row.getValue("firstName"),
    enableSorting: false,
  },
  {
    accessorKey: "lastName",
    header: ({ column }) => <TableColumnHeader column={column} title="Last Name" />,
    cell: ({ row }) => row.getValue("lastName"),
    enableSorting: false,
  },
  {
    accessorKey: "displayName",
    header: ({ column }) => <TableColumnHeader column={column} title="Display Name" />,
    cell: ({ row }) => row.getValue("displayName"),
    enableSorting: false,
  },
  {
    accessorKey: "email",
    header: ({ column }) => <TableColumnHeader column={column} title="Email ID" />,
    cell: ({ row }) => row.getValue("email"),
    enableSorting: false,
  },
  {
    accessorKey: "role",
    header: ({ column }) => <TableColumnHeader column={column} title="Role" />,
    cell: ({ row }) => row.getValue("role"),
    enableSorting: false,
  },
  {
    id: "actions",
    header: "Action",
    cell: ({ row }) => {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <Icons.moreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>Activate</DropdownMenuItem>
            <DropdownMenuItem className="text-destructive">Deactivate</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export default function UsersTable() {
  return (
    <div className="bg-background rounded-sm p-6">
      <div className="mx-auto">
        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" className="p-2">
              <Icons.arrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-foreground text-2xl font-semibold">User Management</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
              <Icons.download className="h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
              <Icons.sortAsc className="h-4 w-4" />
              Sort
            </Button>
            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
              <Icons.filter className="h-4 w-4" />
              Filter
            </Button>
          </div>
        </div>

        {/* Table */}
        <div className="bg-card rounded-lg border-none">
          <TableProvider columns={columns} data={userData}>
            <TableHeader className="border-none">
              {({ headerGroup }) => (
                <TableHeaderGroup headerGroup={headerGroup}>
                  {({ header }) => <TableHead header={header} className="" />}
                </TableHeaderGroup>
              )}
            </TableHeader>
            <TableBody>
              {({ row }) => (
                <TableRow row={row} className="my-4 rounded-md border-none bg-[#f0f7f6]">
                  {({ cell }) => (
                    <TableCell
                      cell={cell}
                      className="flex items-center justify-center px-6 py-4 text-left font-medium"
                    />
                  )}
                </TableRow>
              )}
            </TableBody>
          </TableProvider>
        </div>
      </div>
    </div>
  );
}
