"use client";
import React, { useState } from "react";
import { SearchBar } from "@/components/search-bar";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import { SubNavBar } from "@/components/sub-nav-bar";
import UsersTable from "../user-list";
import AddUserFormView from "./add-user-form-view";

const UserList: React.FC = () => {
  const [open, setOpen] = useState(false);

  return (
    <div className="h-[90vh] w-full bg-[#f0f7f6] p-10">
      <SearchBar />

      <Dialog open={open} onOpenChange={setOpen}>
        <div className="py-3">
          <SubNavBar actionButton={{ name: "Add User", onClick: () => setOpen(true) }} />
        </div>

        <UsersTable />

        <DialogContent className="sm:max-w-[500px]">
          <AddUserFormView />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserList;
