version: "3.7"

services:
  supertokens:
    image: supertokens/supertokens-postgresql
    container_name: supertokens
    restart: unless-stopped
    ports:
      - "3567:3567"
    environment:
      POSTGRESQL_USER: azure_root
      POSTGRESQL_PASSWORD: <PERSON><PERSON><PERSON>@2111
      POSTGRESQL_HOST: testserver96.postgres.database.azure.com
      POSTGRESQL_PORT: 5432
      POSTGRESQL_DATABASE_NAME: postgres
      POSTGRESQL_TABLE_SCHEMA: auth
# postgresql://citus:<EMAIL>/citus?statusColor=686B6F&env=&name=aazure&tLSMode=0&usePrivateKey=false&safeModeLevel=0&advancedSafeModeLevel=0&driverVersion=0&lazyload=false
